["tests/test_datasets.py::test_forward_sample_data", "tests/test_datasets.py::test_forward_sample_dimensions", "tests/test_datasets.py::test_stock_prediction_dataset", "tests/test_datasets.py::test_stock_prices_dataset", "tests/test_utils.py::test_data_string_to_float", "tests/test_utils.py::test_status_calc", "tests/test_variables.py::test_features_same", "tests/test_variables.py::test_outperformance", "tests/test_variables.py::test_statspath"]